{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e2883145a8b35df03595866432e6e369", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98950bda50ab3de207ce20e9af19523ab4", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9809d31180dd6c361d12ce76b9698ed197", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981e02c2159df410eabb028ef94ec2f3d9", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9809d31180dd6c361d12ce76b9698ed197", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9877f29b0e8efc6b636fabbf91420fc727", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98192f12b9adea4ca1d59bdbd4a54d03b3", "guid": "bfdfe7dc352907fc980b868725387e987eb284477eeb856ec3bb6d0e6cc41cce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee394d64b57cbcf4bb38029b4c817038", "guid": "bfdfe7dc352907fc980b868725387e98ea8d996b500de73d3703fb7dba8bf62d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da272ac8d5bf242e7a845da77655d73d", "guid": "bfdfe7dc352907fc980b868725387e981b977123b6f24af09bb1f0ebefdb85cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d6464333d3e782df71fffc9235eebb4", "guid": "bfdfe7dc352907fc980b868725387e98adf69e9121dd6a6dc2b46ece7b196cf9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d703019690f32fac9bda10e6a6cec798", "guid": "bfdfe7dc352907fc980b868725387e98c45ad4046f1d26b8d898cded184a5af6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851a83049296588c6ca06c24a77a1f6a2", "guid": "bfdfe7dc352907fc980b868725387e98bb8217cfe091698c7c0b3b2eda6d407d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98706cb289ff258fe746807fb990151a0b", "guid": "bfdfe7dc352907fc980b868725387e9879967975547e66ead092ed5c64951c35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd433540e0a29f564820b6f7824c2cd8", "guid": "bfdfe7dc352907fc980b868725387e982bb95ab662c4e93434684f49144dbf0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7dcb3b1e831061f14cf96891ec26e25", "guid": "bfdfe7dc352907fc980b868725387e98097a9d104faaff8552a930e87c755c2e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878b0137e3484a3571d34b8037ad9ffd6", "guid": "bfdfe7dc352907fc980b868725387e989c7f36a0c69231b3e480fa775adab6e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870a889dcad5d2be491c4b2273b76576b", "guid": "bfdfe7dc352907fc980b868725387e983b7ad1a96cde26e0e9f642f0cf0b873e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98baebf79f6b4bc167c19bece06c145fee", "guid": "bfdfe7dc352907fc980b868725387e98306f4cf39e4331e345e72a510c1a14aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984685b362eeef6578a115d6627cb45649", "guid": "bfdfe7dc352907fc980b868725387e98235a352e444cc9782926176d94b121f7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b12e3a1d5e2691239fbc7e38d4d675b4", "guid": "bfdfe7dc352907fc980b868725387e98eb3882b92788792376c67585a1dd0d45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838584d947655a75ecb3fc6a17b7e7534", "guid": "bfdfe7dc352907fc980b868725387e9876d235654159b6d84776493c9ea9fb91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841d512d53ad487d79f881a6fcac2e7f7", "guid": "bfdfe7dc352907fc980b868725387e983dc979e498aa61b1745d5e0d76e831e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e30ccdbe2f408b39b449ce5143193a0", "guid": "bfdfe7dc352907fc980b868725387e983748a063974d91353887a3f8f3d3796d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2999d002c8f24fc109814c20c820329", "guid": "bfdfe7dc352907fc980b868725387e98d6fa2442c3a1a632d95360d514d204b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832a1bd3f831be7d54f613959d605ba38", "guid": "bfdfe7dc352907fc980b868725387e98248b5a83857ba32ed0e853ff8f391043", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828937a27c691a96021e02b93f7fff12a", "guid": "bfdfe7dc352907fc980b868725387e98947897eb09f9bd390df520bede63bf46", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f91d62b95990d54bacd496984290e7c3", "guid": "bfdfe7dc352907fc980b868725387e98731223b4e2a1e4756e1385d5c2daf8cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8ed629fe432f9e1c1b03e9ee40ddc67", "guid": "bfdfe7dc352907fc980b868725387e98211e7b35dc7e38489c822a6525cc16c5", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98c8f8013d8edc1c5f856b57554184c8cc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980519094d61a53451a0846799b03be66a", "guid": "bfdfe7dc352907fc980b868725387e98610ad72bbe7c3e1cc055d5d9c5da891c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e34b7f7a88b25c5deda28c24466bddb1", "guid": "bfdfe7dc352907fc980b868725387e983fed595843405937d7bf6cae03539610"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878212c868e21c5a18d4c56207160c3ae", "guid": "bfdfe7dc352907fc980b868725387e98a18f7261d37d59eebe6b6df80c0d7b4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6cbe8ae7153ca676d82a8aaf0ce5bf2", "guid": "bfdfe7dc352907fc980b868725387e98853b762b2625e0f8df33de64b1236a97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6d59ef11c0987cb4e03a0316ea20f1d", "guid": "bfdfe7dc352907fc980b868725387e98e29d1515e6ca78e3e179bf3414cfb7fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd504d96a7ca8f503bbe54692f7cb683", "guid": "bfdfe7dc352907fc980b868725387e98a573939f370debf3297838d52e358b47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862dd002434bf8491428480f208599576", "guid": "bfdfe7dc352907fc980b868725387e982e6b56841f1d0324e5096b8ceea43128"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886f5fd35062cbd2cbd56d527721bd353", "guid": "bfdfe7dc352907fc980b868725387e987a3e0fc8567d66f164d4760e3f05b38a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980585129870fd9dc332934d8c27b5534a", "guid": "bfdfe7dc352907fc980b868725387e98d32005e71a2c9a8d60e9eb68adf21a02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982016f77958f285f51575bfb424172316", "guid": "bfdfe7dc352907fc980b868725387e983f2dd13393e18afbf9c71a2fce3a8d75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e89cfcfe73f80536305a0337d1420a4", "guid": "bfdfe7dc352907fc980b868725387e98890f0f6232fa56b6624dc2aa6187a7f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98426d224933158be771c2f1a86027e3b0", "guid": "bfdfe7dc352907fc980b868725387e98c69bc87756120c9556d352a55fe9bc0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcc0a849a525e59521775f549abf581d", "guid": "bfdfe7dc352907fc980b868725387e985d96f29b693b6d5acad329d56120a361"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856674ec0889bdbfb238240b1e38a5f32", "guid": "bfdfe7dc352907fc980b868725387e98a6f1a38bd05d265e390f51a0299f861e"}], "guid": "bfdfe7dc352907fc980b868725387e9847969bc984f492696f197cac6c0d90b6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e9869c622537f9843964ab159ca7c87b3c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab997f728dfc73763c7be192e3d19493", "guid": "bfdfe7dc352907fc980b868725387e987574c5edbdb235899bfac5302fc0f551"}], "guid": "bfdfe7dc352907fc980b868725387e9883d205751495305b09ce585ba97a0f08", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98af83b03a4a4da70dc7ef0051f369db3e", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e983612a17a0cd7fd71be380b4d882174e5", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}