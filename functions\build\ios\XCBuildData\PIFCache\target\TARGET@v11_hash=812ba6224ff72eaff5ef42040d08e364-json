{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f42038dc6aeeae4fe30c702eadae21b7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98242120bf341678c7352304737bc1e5c3", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f94133b8d0e7c19d0720d442c8d5a6d8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987590c6175d363f08366951b05c4d9994", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f94133b8d0e7c19d0720d442c8d5a6d8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9845b1ad10744a168ca81e6c2da92b83df", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987804874f8839a99122a6693d9c8ecf85", "guid": "bfdfe7dc352907fc980b868725387e98ea38d420f3d3bd406b245b6060557a55", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8dae6bf5d2df212382e83168f05a033", "guid": "bfdfe7dc352907fc980b868725387e9818e890eae861290db4559708c970c218", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c653267512b714acd65f286ac2b5f421", "guid": "bfdfe7dc352907fc980b868725387e9857f2fdf2b6d6ef1b07a2d98523c319bd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984489250ef8ddd91a5eac1554e28ef1cd", "guid": "bfdfe7dc352907fc980b868725387e985e6c64b8795619efabcff267bfea8b57", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4d776cec4cecab4a1d845e428ecb7f6", "guid": "bfdfe7dc352907fc980b868725387e98ff048e7f9f78112308f9d7940e110f21", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc4cf96199d0acdb915b5cb86ab61266", "guid": "bfdfe7dc352907fc980b868725387e98ba1f8d3f1c4f147c820995cc7cfd774e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fa79cfba137a489ebb599ea0d99df92", "guid": "bfdfe7dc352907fc980b868725387e98f6e52fac22113e7b07705d28f8aff178", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c07f2b80868204b0e4dbab80e54c874a", "guid": "bfdfe7dc352907fc980b868725387e98d76578715e0e06dc9b45a64f5f5e00f1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98775136faf182afe12c61f300a0e93c96", "guid": "bfdfe7dc352907fc980b868725387e98f214d5afc4b395f0819da62943c42b26", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817817fb0b6ab1e465cd02981adfc4ed8", "guid": "bfdfe7dc352907fc980b868725387e983c50ab350b5dbdd33e1e78a31abfaa87", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5f045ceeb6f4a84763bab5cf935b203", "guid": "bfdfe7dc352907fc980b868725387e9890bbcf760d3dd2f1d5cb883159fcebaf", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98abb7da5ffcca6361d7bd9d5f0c3f0f7c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989c1d1222661b074c96f7e28fab207d64", "guid": "bfdfe7dc352907fc980b868725387e98e0bfd4b93baa26fc9024ee18d5467c66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824d4c2f45f5ae764aeef5dd1150762f5", "guid": "bfdfe7dc352907fc980b868725387e98a94dc4f23bec56fee981175af3d933b9"}], "guid": "bfdfe7dc352907fc980b868725387e98223b61c11f51b49e5384f8e8699b5d84", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e98a37f4cbc1683b0b77a6f88ca990a9333"}], "guid": "bfdfe7dc352907fc980b868725387e98bc99305deac6e9ae4507916579f95c73", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98ed7cd5216b19d44c90040699425ba9b9", "targetReference": "bfdfe7dc352907fc980b868725387e98c04ead258c2ba3f656422d1784107881"}], "guid": "bfdfe7dc352907fc980b868725387e98dfdc9c639b3195d7b6da6c28cd508cc9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98c04ead258c2ba3f656422d1784107881", "name": "FirebaseCoreExtension-FirebaseCoreExtension_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98311e6292af5af43c801705cd189cc184", "name": "FirebaseCoreExtension.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}