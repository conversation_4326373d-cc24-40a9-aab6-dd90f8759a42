import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:problem_perception_landing/design/app_colors.dart';
import 'package:problem_perception_landing/features/auth/bloc/auth_bloc.dart';
import 'package:problem_perception_landing/features/auth/presentation/auth_flow_widget.dart';
import 'package:problem_perception_landing/features/favorites/presentation/saved_page.dart';
import 'package:problem_perception_landing/features/feedback/presentation/feedback_dialog.dart';
import 'package:problem_perception_landing/features/home/<USER>/home_page.dart';
import 'package:problem_perception_landing/features/home/<USER>/widgets/comments_drawer.dart';
import 'package:problem_perception_landing/features/home/<USER>/widgets/solution_drawer.dart';
import 'package:problem_perception_landing/features/onboarding/presentation/onboarding_dialog.dart';
import 'package:problem_perception_landing/features/profile/presentation/profile_page.dart';

class AppNavigationRail extends StatefulWidget {
  const AppNavigationRail({super.key});

  @override
  State<AppNavigationRail> createState() => _AppNavigationRailState();
}

class _AppNavigationRailState extends State<AppNavigationRail> {
  int _selectedIndex = 0;
  NavigationRailLabelType labelType = NavigationRailLabelType.all;
  double groupAlignment = -1.0;
  final List<Widget> _pages = const [
    HomePage(),
    SavedPage(),
    ProfilePage(),
  ];

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthBloc, AuthState>(
      listener: (dialogContext, state) async {
        debugPrint('Auth state: ${state.status}, email: ${state.user?.email ?? 'no email'}');
        switch (state.status) {
          case AuthStatus.unauthenticated:
            await showDialog(
              barrierDismissible: false,
              context: context,
              builder: (context) {
                return const AuthFlowWidget();
              },
            );
          case AuthStatus.authenticatedAndPaid:
            while (Navigator.canPop(dialogContext)) {
              Navigator.pop(dialogContext);
            }
          // await showDialog(
          //   barrierDismissible: false,
          //   context: context,
          //   builder: (context) {
          //     return const ResultsLoadingDialog();
          //   },
          // );
          case AuthStatus.paymentRequired:
            //..................
            while (Navigator.canPop(dialogContext)) {
              Navigator.pop(dialogContext);
            }
          //.................
          // await showDialog(
          //   barrierDismissible: false,
          //   context: context,
          //   builder: (context) {
          //     return const AuthFlowWidget();
          //   },
          // );
          case AuthStatus.profileRequired:
            await showDialog(
              barrierDismissible: false,
              context: context,
              builder: (context) {
                return const OnboardingDialog();
              },
            );
          case AuthStatus.failure:
            break;
          case AuthStatus.loading:
            break;
          case AuthStatus.initial:
            await showDialog(
              barrierDismissible: false,
              context: context,
              builder: (context) {
                return const AuthFlowWidget();
              },
            );
        }
      },
      child: LayoutBuilder(builder: (context, constraints) {
        if (constraints.maxWidth > 600) {
          return Scaffold(
            resizeToAvoidBottomInset: false,
            drawer: const SolutionDrawer(),
            endDrawer: const CommentsDrawer(),
            body: Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                NavigationRail(
                  indicatorColor: AppColors.darkGrey,
                  // indicator shape is a circle
                  indicatorShape: const CircleBorder(),
                  backgroundColor: AppColors.black,
                  selectedIndex: _selectedIndex,
                  groupAlignment: groupAlignment,
                  onDestinationSelected: (int index) {
                    setState(() {
                      _selectedIndex = index;
                    });
                  },
                  labelType: NavigationRailLabelType.all,
                  destinations: const <NavigationRailDestination>[
                    NavigationRailDestination(
                      indicatorColor: AppColors.secondaryColor,
                      padding: EdgeInsets.fromLTRB(16, 32, 16, 16),
                      icon: Icon(
                        Icons.home_outlined,
                        color: Colors.white,
                      ),
                      selectedIcon: Icon(Icons.home_outlined, color: AppColors.primaryColor),
                      label: SizedBox(),
                    ),
                    NavigationRailDestination(
                      padding: EdgeInsets.all(16),
                      icon: Icon(Icons.bookmark_border, color: Colors.white),
                      selectedIcon: Icon(Icons.bookmark_border, color: AppColors.primaryColor),
                      label: SizedBox(),
                    ),
                    // NavigationRailDestination(
                    //   icon: Icon(Icons.history),
                    //   selectedIcon: Icon(Icons.history),
                    //   label: SizedBox(),
                    // ),
                    NavigationRailDestination(
                      padding: EdgeInsets.all(16),
                      icon: Icon(Icons.person_outline, color: Colors.white),
                      selectedIcon: Icon(Icons.person_outline, color: AppColors.primaryColor),
                      label: SizedBox(),
                    ),
                  ],
                ),
                _pages[_selectedIndex],
              ],
            ),
          );
        } else {
          return Scaffold(
            drawer: const SolutionDrawer(),
            endDrawer: const CommentsDrawer(),
            body: Stack(
              children: [
                _pages[_selectedIndex],
                Positioned(
                  bottom: 4,
                  right: 4,
                  child: Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: FloatingActionButton(
                      backgroundColor: AppColors.accentColor,
                      onPressed: () async {
                        await showDialog(
                            context: context,
                            builder: (context) {
                              return FeedbackDialog();
                            });
                      },
                      child: const Icon(Icons.feedback_outlined),
                    ),
                  ),
                ),
              ],
            ),
            bottomNavigationBar: BottomNavigationBar(
              currentIndex: _selectedIndex,
              onTap: (int index) {
                setState(() {
                  _selectedIndex = index;
                });
              },
              items: const [
                BottomNavigationBarItem(
                  icon: Icon(Icons.home),
                  label: 'Home',
                ),
                BottomNavigationBarItem(
                  icon: Icon(Icons.bookmark_border),
                  label: 'Saved',
                ),
                // BottomNavigationBarItem(
                //   icon: Icon(Icons.history),
                //   label: 'History',
                // ),
                BottomNavigationBarItem(
                  icon: Icon(Icons.person),
                  label: 'Profile',
                ),
              ],
            ),
          );
        }
      }),
    );
  }
}
