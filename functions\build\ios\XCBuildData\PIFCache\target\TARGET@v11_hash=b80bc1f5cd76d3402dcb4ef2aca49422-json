{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98908a150b4cbf048c438e93c46a81846b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d549605a0ff0397b4fd1b7bc9c1403f2", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982b9ff668fe87f360f949b01da3752ac7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980d0bcf27745d37d5d60d17660dfdf943", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982b9ff668fe87f360f949b01da3752ac7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98151d5d6fdbacf91e53321e3df6920083", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982c91ae9d0d5ea876c98c66bc7d6d5262", "guid": "bfdfe7dc352907fc980b868725387e98211f0a13397fde37d674da520108b052", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982447bf27971314f1b54654402ead796f", "guid": "bfdfe7dc352907fc980b868725387e982e36a857b4baa5bc82fc7d4b09421656", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983babff5bd934b5586db7b6eceb438bcb", "guid": "bfdfe7dc352907fc980b868725387e98a905bdab67b65b3fc95bcdc278e0b839", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a68b077dbca6c58663f28ad431e8463d", "guid": "bfdfe7dc352907fc980b868725387e9886e4cdb2db90e6147a0e9c5a6e0793c5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c185f1bdd81813ff82ce4772bc67be7", "guid": "bfdfe7dc352907fc980b868725387e98786eddd074c4f8089efeb4c54ada45a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7dd1ab2c6e5b7ab6f74f9f3a05e5510", "guid": "bfdfe7dc352907fc980b868725387e98513b48da4a6238a83974f82eefc4d6b4", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98be17d73bb8d5775396cc1633f05fb151", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987307b4869a74d77b1e8aeba919a219fd", "guid": "bfdfe7dc352907fc980b868725387e98f12f6f12c88b2b29b2739afb65df2329"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817be0aceccea2ee179491d3102400dad", "guid": "bfdfe7dc352907fc980b868725387e984b17780b6547d3a5274930e83ce5d0aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872a65930060db6440505287ed346b0d2", "guid": "bfdfe7dc352907fc980b868725387e986dff0a37c8905abc989b4a1bfd60576b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987491bed45cb53301bb0c9172ccacfa78", "guid": "bfdfe7dc352907fc980b868725387e986e58cfdf517ba0ada221c63fdc48e775"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b543aa987fd10f3dc0c9c1aa88e794b8", "guid": "bfdfe7dc352907fc980b868725387e989929d0827c664104c7c10c03ee43940d"}], "guid": "bfdfe7dc352907fc980b868725387e98b6d8da8fc6965c9cd4cd8dd57149f8f8", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e984adb88ae3a066d59f13d4218b1abe323"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857517583cea0339c53d5dd4c2679b551", "guid": "bfdfe7dc352907fc980b868725387e98da6eae6aae5f3a9e8779408a979366ef"}], "guid": "bfdfe7dc352907fc980b868725387e98d6ae39f47fa4067dfb314b9f0f92cf26", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e24d9d1d4a04ce23acf418fa81095605", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}], "guid": "bfdfe7dc352907fc980b868725387e9867e093ded5363739dfa14bffce33acf1", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}