import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
// ignore: depend_on_referenced_packages
import 'package:flutter_web_plugins/url_strategy.dart';
import 'package:problem_perception_landing/core/router/router.dart';
import 'package:problem_perception_landing/design/theme.dart';
import 'package:problem_perception_landing/features/customize_pain_points/bloc/customize_pain_points_bloc.dart';
import 'package:problem_perception_landing/features/feedback/bloc/feedback_bloc.dart';
import 'package:problem_perception_landing/features/onboarding/bloc/onboarding_bloc.dart';
import 'package:problem_perception_landing/features/payment/bloc/payment_bloc.dart';
import 'package:problem_perception_landing/features/results/bloc/results_bloc.dart';
import 'package:problem_perception_landing/firebase_options.dart';
// import 'package:problem_perception_landing/firebase_options.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import 'features/auth/bloc/auth_bloc.dart';
import 'features/favorites/bloc/saved_bloc.dart';
import 'features/profile/bloc/profile_bloc.dart';
import 'service_locator.dart' as di;

void main() async {
  if (!kDebugMode) {
    await SentryFlutter.init(
      (options) {
        options.dsn =
            'https://<EMAIL>/4507923240976464';
        // Set tracesSampleRate to 1.0 to capture 100% of transactions for tracing.
        // We recommend adjusting this value in production.
        options.tracesSampleRate = 1.0;
        // The sampling rate for profiling is relative to tracesSampleRate
        // Setting to 1.0 will profile 100% of sampled transactions:
        options.profilesSampleRate = 1.0;
      },
      appRunner: () => initApp(),
    );
  } else {
    initApp();
  }
}
void setupFirebaseEmulator() {
  const int functionsPort = 5001;

  // 👇 your laptop’s LAN IP
  const host = '**************';

  FirebaseFunctions.instance.useFunctionsEmulator(host, functionsPort);
  debugPrint('✅ Using Cloud Functions emulator on $host:$functionsPort');
}

void initApp() async {
  // ignore: prefer_const_constructors
  setUrlStrategy(PathUrlStrategy());
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

    // 🔥 Point Functions to local emulator (only in debug)
  setupFirebaseEmulator();
  di.init();
  runApp(MultiBlocProvider(
    providers: [
      BlocProvider<ResultsBloc>(
        create: (context) => di.sl<ResultsBloc>(),
      ),
      BlocProvider<AuthBloc>(
        create: (context) => di.sl<AuthBloc>(),
      ),
      BlocProvider<SavedBloc>(
        create: (context) => di.sl<SavedBloc>(),
      ),
      BlocProvider<ProfileBloc>(
        create: (context) => di.sl<ProfileBloc>(),
      ),
      BlocProvider<PaymentBloc>(
        create: (context) => di.sl<PaymentBloc>(),
      ),
      BlocProvider<FeedbackBloc>(
        create: (context) => di.sl<FeedbackBloc>(),
      ),
      BlocProvider<OnboardingBloc>(
        create: (context) => di.sl<OnboardingBloc>(),
      ),
      BlocProvider<CustomizePainPointsBloc>(
        create: (context) => di.sl<CustomizePainPointsBloc>(),
      ),
    ],
    child: const MyApp(),
  ));
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      routerConfig: AppRouter.router,
      title: 'Problem Perception',
      theme: AppTheme.getTheme(context),
      debugShowCheckedModeBanner: false,
    );
  }
}
