{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c77fe5390e80f5ac0e8797bb544d2a62", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseMessagingInterop/FirebaseMessagingInterop-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseMessagingInterop/FirebaseMessagingInterop-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessagingInterop/FirebaseMessagingInterop.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessagingInterop", "PRODUCT_NAME": "FirebaseMessagingInterop", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989f42512b1dd80892b5011fd83ae05062", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f688b0d02e5f9f26551b3f685427df52", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseMessagingInterop/FirebaseMessagingInterop-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseMessagingInterop/FirebaseMessagingInterop-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessagingInterop/FirebaseMessagingInterop.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessagingInterop", "PRODUCT_NAME": "FirebaseMessagingInterop", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98061d58126e04cfccba99e124b2f8624c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f688b0d02e5f9f26551b3f685427df52", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseMessagingInterop/FirebaseMessagingInterop-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseMessagingInterop/FirebaseMessagingInterop-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessagingInterop/FirebaseMessagingInterop.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessagingInterop", "PRODUCT_NAME": "FirebaseMessagingInterop", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982fde8240360641f8144613d8d475b82d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986903c45c3b97f503ec192c04ef9622fc", "guid": "bfdfe7dc352907fc980b868725387e981ed6d35e991a4fa069a58940e3d4f1da", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876f398f3baacb39edfba0bdd71c4e062", "guid": "bfdfe7dc352907fc980b868725387e989d1abdc065888f06f1dbff8bc5bdcabe", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983071cae8954767f98bb842ad8a1464f3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b46811ebe4cf72e33ce5877647c61347", "guid": "bfdfe7dc352907fc980b868725387e988c968ac2855128dcb1cb726793641925"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980914e5cccb5e71a99ebd776ec836b0ee", "guid": "bfdfe7dc352907fc980b868725387e98e9f5079980d3d6105ed780f88ef53e64"}], "guid": "bfdfe7dc352907fc980b868725387e989c571766e80f1c292241ad457dd70717", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e98c9def656568d99094a5a336c1c704ea9"}], "guid": "bfdfe7dc352907fc980b868725387e9812101a6138d8dda8707c1b67d3a1c9b2", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98716c6a624487885553351bd3924aff69", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9863397a73468de8eb61b23cd9f8f91a11", "name": "FirebaseMessagingInterop", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e982d9e86a8994e982511da4431a702f90b", "name": "FirebaseMessagingInterop.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}