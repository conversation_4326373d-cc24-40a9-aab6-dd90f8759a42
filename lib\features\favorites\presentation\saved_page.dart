import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:problem_perception_landing/core/widgets/web_body_template.dart';
import 'package:problem_perception_landing/features/favorites/bloc/saved_bloc.dart';
import 'package:problem_perception_landing/features/favorites/presentation/widgets/new_saved_card.dart';

import '../../results/models/result_model.dart';

class SavedPage extends StatefulWidget {
  const SavedPage({super.key});

  @override
  State<SavedPage> createState() => _SavedPageState();
}

class _SavedPageState extends State<SavedPage> {
  @override
  void initState() {
    super.initState();
    context.read<SavedBloc>().add(LoadFavorites(uid: FirebaseAuth.instance.currentUser!.uid));
  }

  @override
  Widget build(BuildContext context) {
    return WebBodyTemplate(
      children: [
        Text(
          'Saved Pain Points',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 32),
        BlocConsumer<SavedBloc, SavedState>(
          listener: (context, state) {
            if (state.status == SavedStatus.failure) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(state.errorMessage)),
              );
            }
          },
          builder: (context, state) {
            if (state.status == SavedStatus.loading) {
              return const Center(child: CircularProgressIndicator());
            } else if (state.status == SavedStatus.failure) {
              return Center(child: Text('Error: ${state.errorMessage}'));
            } else if (state.status == SavedStatus.success && state.groupedResults.isNotEmpty) {
              return ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: state.groupedResults.length,
                itemBuilder: (context, index) {
                  String businessIdea = state.groupedResults.keys.elementAt(index);
                  List<ResultModel> results = state.groupedResults[businessIdea] ?? [];

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          SvgPicture.asset('assets/images/svgs/sparkles_black.svg'),
                          const SizedBox(width: 8),
                          Text(
                            businessIdea.isNotEmpty
                                ? '${businessIdea[0].toUpperCase()}${businessIdea.substring(1)}'
                                : businessIdea,
                            style: Theme.of(context).textTheme.headlineSmall,
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      if (results.isNotEmpty) Text(results[0].timestamp?.substring(0, 10) ?? ''),
                      const SizedBox(height: 8),
                      ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: results.length,
                        itemBuilder: (context, resultIndex) {
                          return SavedCard2(
                            resultModel: results[resultIndex],
                          );
                        },
                      ),
                      const SizedBox(height: 16),
                    ],
                  );
                },
              );
            } else {
              return const Center(child: Text('🙁 No saved pain points yet'));
            }
          },
        ),
      ],
    );
  }
}
