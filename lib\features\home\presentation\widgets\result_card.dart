import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:problem_perception_landing/core/services/analytics_service.dart';
import 'package:problem_perception_landing/core/widgets/app_icon_button.dart';
import 'package:problem_perception_landing/core/widgets/base_dialog.dart';
import 'package:problem_perception_landing/core/widgets/mini_button.dart';
import 'package:problem_perception_landing/design/app_colors.dart';
import 'package:problem_perception_landing/features/results/bloc/results_bloc.dart';
import 'package:problem_perception_landing/service_locator.dart';
import 'package:step_progress_indicator/step_progress_indicator.dart';

import '../../../results/models/result_model.dart';

class ResultCard2 extends StatefulWidget {
  final ResultModel resultModel;
  const ResultCard2({super.key, required this.resultModel});

  @override
  State<ResultCard2> createState() => _ResultCard2State();
}

class _ResultCard2State extends State<ResultCard2> {
  bool isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
      child: Stack(
        children: [
          Container(
            padding: const EdgeInsets.fromLTRB(20, 20, 20, 0),
            margin: const EdgeInsets.only(bottom: 25),
            decoration: BoxDecoration(
              color: const Color.fromRGBO(255, 255, 255, 1),
              border: Border.all(color: const Color(0xFFE8E8E8)),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.2),
                  spreadRadius: 1,
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
              borderRadius: BorderRadius.circular(20),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: SelectableText(widget.resultModel.painPoint, style: Theme.of(context).textTheme.headlineSmall),
                    ),
                    const SizedBox(width: 8),
                    GestureDetector(
                      onTap: () async {
                        await showDialog(
                            context: context,
                            builder: (context) {
                              return BaseDialog(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                                  children: [
                                    Text(
                                      'Scoring System',
                                      style: Theme.of(context).textTheme.titleSmall,
                                    ),
                                    SizedBox(height: width > 900 ? 40 : 16),
                                    Text(
                                      'The score is calculated on a number of different factors. One example is the number of times this pain point has been reported by users. The higher the score, the more common the pain point is. However, the most impressive part of our scoring system is the sentiment analysis performed by Artificial Intelligence (AI). The AI is able to detect sentiment, and as a result inform you of how much pain the user experiences, based on the comments and clues they have left on the internet.',
                                      style: Theme.of(context).textTheme.bodyMedium,
                                    ),
                                    SizedBox(height: width > 900 ? 40 : 16),
                                    ElevatedButton(
                                        onPressed: () {
                                          Navigator.pop(context);
                                        },
                                        child: const Text('Close'))
                                  ],
                                ),
                              );
                            });
                      },
                      child: Tooltip(
                        message: 'Tap to learn more',
                        child: CircularStepProgressIndicator(
                          totalSteps: 100,
                          currentStep: widget.resultModel.score,
                          stepSize: 5,
                          selectedColor: Colors.greenAccent,
                          unselectedColor: Colors.grey[200],
                          padding: 0,
                          width: 50,
                          height: 50,
                          selectedStepSize: 5,
                          roundedCap: (_, __) => true,
                          child: Center(
                            child: Text(
                              widget.resultModel.score.toString(),
                              // style: Theme.of(context).textTheme.bodySmall,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 180),
                  child: MiniButton(
                    onTap: () {
                      context.read<ResultsBloc>().add(ResultSelected(result: widget.resultModel));
                      Scaffold.of(context).openEndDrawer();
                      sl<AnalyticsService>().logReferencesClicked(
                        widget.resultModel.uid,
                      );
                    },
                    title: 'References',
                    isTransparent: true,
                    icon: Container(
                      decoration: BoxDecoration(
                        color: AppColors.darkGrey,
                        borderRadius: BorderRadius.circular(50),
                      ),
                      child: const Icon(Icons.arrow_forward_outlined, color: AppColors.primaryColor),
                    ),
                  ),
                ),
                SizedBox(height: width > 800 ? 16 : 36),
              ],
            ),
          ),
          Positioned(
              bottom: 12,
              right: 10,
              child: Container(
                decoration: BoxDecoration(
                  color: const Color.fromRGBO(255, 255, 255, 1),
                  border: Border.all(color: const Color(0xFFE8E8E8)),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.2),
                      spreadRadius: 1,
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      AppIconButton(
                          tooltipMessage: 'Save',
                          icon: widget.resultModel.saved == true
                              ? const Icon(
                                  Icons.done,
                                  size: 12,
                                )
                              : const Icon(
                                  Icons.bookmark_border,
                                  size: 12,
                                ),
                          onTap: () {
                            context.read<ResultsBloc>().add(widget.resultModel.saved == true
                                ? RemoveFromFavorites(result: widget.resultModel)
                                : SaveToFavorites(result: widget.resultModel));
                          }),
                      const SizedBox(width: 8),
                      AppIconButton(
                        tooltipMessage: 'Copy to clipboard',
                        icon: const Icon(Icons.ios_share, size: 12),
                        onTap: () {
                          Clipboard.setData(
                            ClipboardData(
                              text: widget.resultModel.painPoint + (widget.resultModel.suggestedSolution ?? ''),
                            ),
                          );
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Copied to clipboard'),
                            ),
                          );
                        },
                      ),
                      const SizedBox(width: 8),
                      AppIconButton(
                        tooltipMessage: 'New search',
                        icon: const Icon(Icons.restore_sharp, size: 12),
                        onTap: () {
                          context.read<ResultsBloc>().add(const NewSearchEvent());
                        },
                      ),
                      const SizedBox(width: 8),
                      AppIconButton(
                        tooltipMessage: 'Good response',
                        icon: const Icon(Icons.thumb_up, size: 12),
                        onTap: () {
                          sl<AnalyticsService>().logPainPointLiked(widget.resultModel.uid, widget.resultModel.painPoint);
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Response liked'),
                            ),
                          );
                        },
                      ),
                      const SizedBox(width: 8),
                      AppIconButton(
                        tooltipMessage: 'Bad response',
                        icon: const Icon(Icons.thumb_down, size: 12),
                        onTap: () {
                          sl<AnalyticsService>().logPainPointDisliked(widget.resultModel.uid, widget.resultModel.painPoint);
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Response disliked'),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              )),
        ],
      ),
    );
  }
}
