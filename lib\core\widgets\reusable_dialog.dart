import 'package:flutter/material.dart';

import 'mini_button.dart';

class ReusableDialog extends StatelessWidget {
  final String title;
  final Widget child;
  final String? buttonText;
  const ReusableDialog({super.key, required this.child, required this.title, this.buttonText});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    // Determine the width and height for different screen sizes
    double dialogWidth;
    double dialogHeight;

    if (screenWidth < 600) {
      // On small screens (e.g., mobile)
      dialogWidth = screenWidth * 0.9; // Takes 90% of the screen width
      dialogHeight = screenHeight * 0.85; // Takes 80% of the screen height
    } else {
      // On larger screens (e.g., tablet, desktop)
      dialogWidth = screenWidth * 0.5; // Takes 50% of the screen width
      dialogHeight = screenHeight * 0.85; // Fixed at 76% height
    }
    return Dialog(
      child: ConstrainedBox(
        constraints: BoxConstraints(
          // minHeight: dialogHeight,
          // minWidth: dialogWidth,
          maxHeight: dialogHeight,
          maxWidth: dialogWidth,
        ),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(title, style: Theme.of(context).textTheme.headlineMedium),
                    MouseRegion(
                      cursor: SystemMouseCursors.click,
                      child: GestureDetector(
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                        child: const Icon(Icons.close),
                      ),
                    )
                  ],
                ),
                const SizedBox(height: 24),
                child,
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    MiniButton(
                        onTap: () {
                          Navigator.of(context).popUntil((route) => route.isFirst);
                        },
                        title: buttonText ?? 'Ok')
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
