import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:problem_perception_landing/core/widgets/web_body_template.dart';
import 'package:problem_perception_landing/design/app_colors.dart';
import 'package:problem_perception_landing/service_locator.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../core/services/analytics_service.dart';
import '../../home/<USER>/widgets/result_card.dart';
import '../bloc/results_bloc.dart';

class ResultsPage extends StatelessWidget {
  const ResultsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return WebBodyTemplate(children: [
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Pain Points',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          TextButton(
            onPressed: () {
              context.read<ResultsBloc>().add(const NewSearchEvent());
            },
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Row(
                children: [
                  const Icon(Icons.add_circle_outline_rounded,
                      color: AppColors.black),
                  const SizedBox(width: 4),
                  Text(
                    'New Search',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      const SizedBox(height: 24),
      BlocBuilder<ResultsBloc, ResultsState>(
        builder: (context, state) {
          // rich text where the second one can be tapped
          if (state.status == ResultsStatus.success) {
            return Text.rich(
              TextSpan(
                text:
                    'We found ${state.results.length} results for "${state.results.first.businessIdea}" ',
                style: Theme.of(context).textTheme.bodyMedium,
                children: [
                  TextSpan(
                    text: 'How can I get more results?',
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          fontWeight: FontWeight.bold,
                          decoration: TextDecoration.underline,
                        ),
                    mouseCursor: WidgetStateMouseCursor.clickable,
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        launchUrl(Uri.parse(
                            'https://www.loom.com/share/c7cbb725266c4ca688c4f35ec39e725e?sid=53a352b6-1f06-427f-b901-3e3b59b3eb35'));
                        sl<AnalyticsService>().logGetMoreResultsClicked();
                    
                      },
                  ),
                ],
              ),
            );
          } else {
            return const SizedBox();
          }
        },
      ),
      const SizedBox(height: 24),
      BlocBuilder<ResultsBloc, ResultsState>(
        builder: (context, state) {
          if (state.status == ResultsStatus.success) {
            return Column(
              children: state.results
                  .map((result) => ResultCard2(
                        resultModel: result,
                      ))
                  .toList(),
            );
          }
          return const SizedBox();
        },
      ),
    ]);
  }
}
