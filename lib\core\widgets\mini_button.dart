import 'package:flutter/material.dart';
import 'package:problem_perception_landing/design/app_colors.dart';

class MiniButton extends StatelessWidget {
  final void Function() onTap;
  final String title;
  final bool? isTransparent;
  final bool? isMobile;
  final Widget? icon;
  const MiniButton({
    super.key,
    required this.onTap,
    required this.title,
    this.isTransparent,
    this.isMobile,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          height: 40,
          constraints: const BoxConstraints(minWidth: 100),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(32),
            border: isTransparent == true
                ? Border.all(
                    color: AppColors.black,
                  )
                : null,
            color: isTransparent == true ? AppColors.darkGrey : Theme.of(context).primaryColor,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (icon != null)
                SizedBox(
                    height: 40,
                    width: 40,
                    child: Padding(
                      padding: const EdgeInsets.all(2.0),
                      child: icon!,
                    )),
              Padding(
                padding: const EdgeInsets.fromLTRB(8, 8, 16, 8),
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                        color: isTransparent == true ? Colors.white : AppColors.black,
                      ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
