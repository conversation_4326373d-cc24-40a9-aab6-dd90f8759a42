{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ffdc59b0dfb784940328cbb68fa138eb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b5b65037f33adf144a4e1a5471baf7e9", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9814d656c0cf8a4d04d342a6f6fa6572d1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9811572f928348ed119d0eca3597ec6146", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9814d656c0cf8a4d04d342a6f6fa6572d1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ed71fb9aab15bb617daf5c70627c6197", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989fc24e71dee2b81e8c7344be74287e38", "guid": "bfdfe7dc352907fc980b868725387e981ef2e198558ced2a5b5323c29d218a17", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fb5f557b0a0cacbc34562365807a6f7", "guid": "bfdfe7dc352907fc980b868725387e98de19a683ce539e401bca073a851ad0a4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bad35e68d5517bea5bbf3dcf3606ceab", "guid": "bfdfe7dc352907fc980b868725387e9857766916fb039ee1ac5c9f011430e202", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b868d9d16ce355c01c9b3a503dc1989", "guid": "bfdfe7dc352907fc980b868725387e98b7c4c11e04fd04b6b111151df6cbbde2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862292883400656dc16cd1cdae51bacc5", "guid": "bfdfe7dc352907fc980b868725387e9808aa4831bf1ded833fbcb79881ae28e7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98835e3b30c9f628f9f2c8dd3942bbb011", "guid": "bfdfe7dc352907fc980b868725387e980fe4983e6b180740b7758bb1286c901c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846f8f3facc2a70a8fba2ba7cc1c194a7", "guid": "bfdfe7dc352907fc980b868725387e980c680545a44e8fe5b5cd7a2236572048", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d2ce385b009b4e329dc97c0fd808841", "guid": "bfdfe7dc352907fc980b868725387e98e143e0867f249c8c428bdd2ce2656251", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891f0b12e422ae7ed2bb155b18b98422b", "guid": "bfdfe7dc352907fc980b868725387e9853520cae853298936cc9c8ee910a9b17", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdc8497d9e403a61c85aa2ff5f23f7ff", "guid": "bfdfe7dc352907fc980b868725387e984c2b793efa5eb7b1ec2d8ec740ed6106", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c64aa63637cf111be7a3128e08a6cbf", "guid": "bfdfe7dc352907fc980b868725387e98e8e0a58c7ec61cec65808f98a9446156", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987efc2866a470584f7b7a2a79bb1f5361", "guid": "bfdfe7dc352907fc980b868725387e984ab1496396cc37ffc32b3c3859ba5d83", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98956eb0d8414c3a3b4c0208960c7ee37c", "guid": "bfdfe7dc352907fc980b868725387e9874cec355fcfe9a98eed2a034787bbdb7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee88be85e258e8fa2018ca2eb76178d3", "guid": "bfdfe7dc352907fc980b868725387e983ad088f76d2852d03924dbfafe3dc4b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893b0ab2d3badab193612e608c738e8f9", "guid": "bfdfe7dc352907fc980b868725387e9860c87de3f1330bb3dce2d9b88b083a20", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e96c0029cac5fedc7eb73ca8d7057e60", "guid": "bfdfe7dc352907fc980b868725387e98fa078760572eb5807e742bc6da86093a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815c3c73f1f7425aa164ea498b4c41cfc", "guid": "bfdfe7dc352907fc980b868725387e981d9e860183266f4f30452d523a058ee1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7789fc6ec3fc1957dbba0dc307a56c7", "guid": "bfdfe7dc352907fc980b868725387e98ab367efdc18fb1dfb7524d0c06d6bc39", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809c20d02154646cd4c47a93a18b6af8e", "guid": "bfdfe7dc352907fc980b868725387e98dac23be109f22d984e6a1d79d6d97962", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986962b671d29f3ff4bffd368bc8e554ba", "guid": "bfdfe7dc352907fc980b868725387e98a46aa9ffc5fe2581cb632bdf26c312dd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2b0f66708142fd3a302de26e24ee4c3", "guid": "bfdfe7dc352907fc980b868725387e9886ae63f8b77742ce6b0ca4619e20cc10", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982763b53fdb225a055051004106b192ed", "guid": "bfdfe7dc352907fc980b868725387e98d06791798f906492fd4f805a87fdbecd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab63e3de0e493811260e33b4e2009b51", "guid": "bfdfe7dc352907fc980b868725387e98621fab8ffe4609c806b636da8d786617", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e60daa5ed95f81c82adbbb12dcc208c", "guid": "bfdfe7dc352907fc980b868725387e9835f42377600bb8de056044e3e6446af9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840c75d8d0eccd30f8fab6be1d18d0253", "guid": "bfdfe7dc352907fc980b868725387e98be892d5678ef1de3e7460a352c0ed0ac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1f1b70804178f298c0c7fa01c58511a", "guid": "bfdfe7dc352907fc980b868725387e98e05a1e92626123a70f8e01da537319d0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d7e00b73602c211a3e808ffb1f9cca6", "guid": "bfdfe7dc352907fc980b868725387e989679a1b601b0bdce0007910322ebe869", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e267b630122327227a117eb21b0b6168", "guid": "bfdfe7dc352907fc980b868725387e98ad3ae42c8082e43a377f542d9444be1e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815d17e72fdb445b137c16393d9526a73", "guid": "bfdfe7dc352907fc980b868725387e9832aac5a9406798a2821d0eaf829bddce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f42896b156b7b27e86ce6088d307141f", "guid": "bfdfe7dc352907fc980b868725387e98ed0b131f0168b0253144776f409128e6", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989cf5716d40023a1e6be3f38864a83557", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984dff75505b4e5e23130cb289dd1b6c5c", "guid": "bfdfe7dc352907fc980b868725387e985e4eae19dc314ce6c591f4f1f8a4f152"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98821897561221639b22106240bc7190a6", "guid": "bfdfe7dc352907fc980b868725387e98f9b7b3b3a53d690f6c8a485365a2ce0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fec1cafbe69be6d245c90a828b2b069a", "guid": "bfdfe7dc352907fc980b868725387e98502543084c6580f483a6e5d6c9817bb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984505ca32602fa2448136f0ce32f16403", "guid": "bfdfe7dc352907fc980b868725387e986ad62df9adc5693ae0518093c1c19840"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa595bd7b9c8030e2ab3a24506850b98", "guid": "bfdfe7dc352907fc980b868725387e980bb8f5cf5f9fbb993f8aa537dde618d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a643b6abb38f9e4a918efb2ccd775ba", "guid": "bfdfe7dc352907fc980b868725387e988325154193efca9ae0ac5626decb127c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884fc98c64d56b196c78bb30b0bd513c4", "guid": "bfdfe7dc352907fc980b868725387e98abd03f73a14378f1cdcb3e4da359571b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f2ae82385eb5b3ea090785bf046ee54", "guid": "bfdfe7dc352907fc980b868725387e98d26de899d65eb7052a9ad1c6e28abfe2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4eeddd9ffed8ae60043bea380cc2af9", "guid": "bfdfe7dc352907fc980b868725387e981923aa047e3b4a004edc77f2b7c9e8a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98471d8dda99e4227bb271bd1a8eb39a5b", "guid": "bfdfe7dc352907fc980b868725387e988d74d6fae6c133de4f7e818102e8924a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bfeb65b94d42efaf124c19373b08568", "guid": "bfdfe7dc352907fc980b868725387e987441051af9df1bca2587ad2dce5b7fe1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b5d58ab0d5492aefcc7f19412d597e2", "guid": "bfdfe7dc352907fc980b868725387e9865ee89fa318639f24680c23c64b58fd0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d423f47bfb64fb19ab93e9b56d44e4dc", "guid": "bfdfe7dc352907fc980b868725387e9813c77cd40b492360b885348ffdf91c1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a186d0de68ce8a2716d575550e889ee5", "guid": "bfdfe7dc352907fc980b868725387e9818fe090a4196cf326ba98eca48c69ff3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e00e78dcb9dc51a9c61dc6bc444f5ef7", "guid": "bfdfe7dc352907fc980b868725387e982cc8125b170f8cee83e353f67b27e421"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832caa1a883013e874e12b21fb6e3e7db", "guid": "bfdfe7dc352907fc980b868725387e98a1eabea28616ce185ee305be9d5ea0a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a18c27fca63094edc2f0d49b0c01967", "guid": "bfdfe7dc352907fc980b868725387e9805488bafe3610fc218d7471d70a80656"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854d851bfd4ac36d97260c84dc2abb751", "guid": "bfdfe7dc352907fc980b868725387e98f26d0a3b389b6aa52e5960b6c36a2e0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a24d1b67a79f0c272a860efcaeeb972e", "guid": "bfdfe7dc352907fc980b868725387e98f4f7c279954c7a38f3a3269597cabf82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855a99b8ff161b5021b7141c89808ba39", "guid": "bfdfe7dc352907fc980b868725387e984871829cb698829352efa3eabab6e37f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866722e8bd423f77d7ed2750dfdbb5b2d", "guid": "bfdfe7dc352907fc980b868725387e9819ff349712025935b1dff62f8d1f64df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883ece4e1dca476ea42e1ea2c70a585b8", "guid": "bfdfe7dc352907fc980b868725387e987af9c387a0a27a6c8e45af1a29941580"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98465f1a66d03d8fed3d328554704904ff", "guid": "bfdfe7dc352907fc980b868725387e982d80e4c3f6a4aac66fd4b49d2958ef63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98959c84888806ffb3eb081416e6301d9d", "guid": "bfdfe7dc352907fc980b868725387e98f4b6270e4faf5d723a52d40b4fb947a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d295ef6796f9cbb92991a588d291f88", "guid": "bfdfe7dc352907fc980b868725387e98c521a877c3b6798ce73b49542786c2b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894de3277adb2d8ee3796a276f3b31d13", "guid": "bfdfe7dc352907fc980b868725387e98cefaf4ea8245d09c5c1875729fce15cf"}], "guid": "bfdfe7dc352907fc980b868725387e988fc1e78a124d5d1d00b21aaa839f2548", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e9822bec7af158ce384c17d194e676a8c72"}], "guid": "bfdfe7dc352907fc980b868725387e98e65761d5f51f2a53018e4cd00df8f39d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98669b40710b05c21a14176b61bde953d6", "targetReference": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340"}], "guid": "bfdfe7dc352907fc980b868725387e9812be5135d0bad5a3b4d5d34e139c88f5", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340", "name": "FirebaseFirestore-FirebaseFirestore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}], "guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9809dfd848e2259e061e90089e1647f5b7", "name": "FirebaseFirestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}