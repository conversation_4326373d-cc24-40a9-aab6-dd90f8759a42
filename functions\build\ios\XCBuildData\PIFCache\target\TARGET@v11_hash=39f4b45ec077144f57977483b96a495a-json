{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98355da27040886a2b15250ead713e0843", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/developer/apps/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/developer/apps/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981599af408fc1abde1ccd73005d868e16", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98345ed9b41b592316b28b17615d247f0e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/developer/apps/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/developer/apps/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987e7355055f349cf3fe15030cd77cff0f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98345ed9b41b592316b28b17615d247f0e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/developer/apps/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/developer/apps/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98aed4c196729956c8a61d4629928cdad8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985cc30b70429769b6bcbccf7ff52423f3", "guid": "bfdfe7dc352907fc980b868725387e98979a868ec6cb25a6822a4624f9dc5c78", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c46cfc709e7b6e4920012cdd3c1e04cb", "guid": "bfdfe7dc352907fc980b868725387e9873c1107bbd2373c55c8dd448634656fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98559651764f7029a2d5e20d89d8b24992", "guid": "bfdfe7dc352907fc980b868725387e981c1890e94dd51953cb6466881130d273", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864475b6d4ff1353b6c8923960d660144", "guid": "bfdfe7dc352907fc980b868725387e9809dd972ad26079c2e69df006d3d42d81", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9911d42f312105d1d1c873b7f0f973d", "guid": "bfdfe7dc352907fc980b868725387e98fca8b13cae7ecb50f242bb67483106d7", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc71a0a1e268187e58445ab8c4c6e648", "guid": "bfdfe7dc352907fc980b868725387e98ae3ffe4c99ff87b2306f6349ccecd2bf", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982efff6a1af7a631f16cc958c1c488b96", "guid": "bfdfe7dc352907fc980b868725387e98314b7d60d34ace8efc96414958295ab0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c34659bd2904ce08fc25cd56202ca82", "guid": "bfdfe7dc352907fc980b868725387e980ee83ab5d3ed260e90ebf7042e20dfa9", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987740a3b15e0c7f6e7419fcc20d863c64", "guid": "bfdfe7dc352907fc980b868725387e98515e7cf3a6a3bb2f200f31506e63a64b", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885e1f1f4a87af2f7ca6fd0f5cf7b7b39", "guid": "bfdfe7dc352907fc980b868725387e985794ae4d491070e394efd8cb4c8012c1", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e32ddc7d59f01ad96e7af3a21165a905", "guid": "bfdfe7dc352907fc980b868725387e989b643420ccad96122ec96b2c1edf9199", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98824624df46c2af071739a5eb372e1a69", "guid": "bfdfe7dc352907fc980b868725387e984a2818c5d2312165c4e46096024c5c7a", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac9c8c12a0aa3f5c66e4d5b029dc0255", "guid": "bfdfe7dc352907fc980b868725387e98e04e98f3ea57c12781d0930a8113adb2", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f94200cbf241f55326d84b2eff0a953", "guid": "bfdfe7dc352907fc980b868725387e98e3e7a475d88b8557f4acff65ffef4bb1", "headerVisibility": "private"}], "guid": "bfdfe7dc352907fc980b868725387e98b88ef27fd18bb6e3876c3f43db5901ba", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98954c60c464d89c874444c21faf1dc231", "guid": "bfdfe7dc352907fc980b868725387e986c038aa933ee0897ebdb9dceea5baae6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98faa6d1ef742e9d26ca6e5c6ddf7b88fc", "guid": "bfdfe7dc352907fc980b868725387e988e91886a550b1e6aed5f42c032c2033b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983546565f50b4fae54fee38487738aeb7", "guid": "bfdfe7dc352907fc980b868725387e9849bd903d12279a42ca33ea2dbe89dbab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fe3bbf210653b99d6958f034c773d58", "guid": "bfdfe7dc352907fc980b868725387e98b61c5daaad73b8fe18335d19aa825eec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98baee2c766b9a4beabc6b842a79fbc11b", "guid": "bfdfe7dc352907fc980b868725387e981c7d9cdffef10555eba86c6446dd42cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982799c658e0902f5001b65d52d5952545", "guid": "bfdfe7dc352907fc980b868725387e9839d5a9b710fc0e2db371db234e0807be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98280f7f6ff3e6eef2fc6fe97f8021be00", "guid": "bfdfe7dc352907fc980b868725387e98b42872c2273d34a1e4229cb42ca01e3f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821eb83326961748e0be93ee0f70623c7", "guid": "bfdfe7dc352907fc980b868725387e98c53c78d8fe9ea884a31f8cf16299b8b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a9c66392b7317a64b0e41b85401b9ca", "guid": "bfdfe7dc352907fc980b868725387e9850433a164d830edeccacdd6c2ca38bde"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d47bace7f890d0bd7f977bf702b47668", "guid": "bfdfe7dc352907fc980b868725387e9846717045e43d43278f35e4e9f1fffb2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abceb0444ed241972d9c69fb5cc4ce9e", "guid": "bfdfe7dc352907fc980b868725387e981b6edea99fa2500d78187dcfc9cd7965"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987315b2b80f45d23f288f41f56f5c9b90", "guid": "bfdfe7dc352907fc980b868725387e984a39283fcc4ab2fb04c170623e6fa973"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807f9e1c3eaaa401a24ebc3d5635f433c", "guid": "bfdfe7dc352907fc980b868725387e989e62b8f4ccd52db79676d507a480c52d"}], "guid": "bfdfe7dc352907fc980b868725387e9835f4c8f61e73511fc560fa84bf693430", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e988aaf844137aefd0f7dadaa75c6732ac7"}], "guid": "bfdfe7dc352907fc980b868725387e985af968884e986bae48f258bcd28bb9de", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e980d2794352e93f0efd04427d324195dea", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}], "guid": "bfdfe7dc352907fc980b868725387e989840e8244cb75f43b3efe8cd6dec5ec5", "name": "cloud_firestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98321793542cff8793cba84baa893d5044", "name": "cloud_firestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}