{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fcf5ef70edbada710fc05cf724374e73", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseSharedSwift/FirebaseSharedSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSharedSwift/FirebaseSharedSwift.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseSharedSwift", "PRODUCT_NAME": "FirebaseSharedSwift", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9873d8c0b2c6924294de9d3f7e7ab7442b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98611a311da95ab5859e3fcad1c1fafcdd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseSharedSwift/FirebaseSharedSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSharedSwift/FirebaseSharedSwift.modulemap", "PRODUCT_MODULE_NAME": "FirebaseSharedSwift", "PRODUCT_NAME": "FirebaseSharedSwift", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98be1160672264ecfed67648590cf49da5", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98611a311da95ab5859e3fcad1c1fafcdd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseSharedSwift/FirebaseSharedSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSharedSwift/FirebaseSharedSwift.modulemap", "PRODUCT_MODULE_NAME": "FirebaseSharedSwift", "PRODUCT_NAME": "FirebaseSharedSwift", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983f4e34be12f499a28470efc844e0e353", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9894260b375b83a5f66047d56a9748bb26", "guid": "bfdfe7dc352907fc980b868725387e98d756e3459db3785a8b9bb815a8acabfa", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b4acdb674c5a5597f7c6fcef4295b8e6", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987e2fed43b44f1c2ab5e4d59efb2885b9", "guid": "bfdfe7dc352907fc980b868725387e98354d10b798a69b68feaa5eacfc7659a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864105328b9845020e6f63588599fef47", "guid": "bfdfe7dc352907fc980b868725387e989264f48e22d5fe78ad0d34e371918479"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f45c0264988e14b0a80abcc16ae9cf67", "guid": "bfdfe7dc352907fc980b868725387e9851b09bfb6d0a7d9d52d64e751b11651d"}], "guid": "bfdfe7dc352907fc980b868725387e9882e20f547b441cc04d317eb605728e05", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e98af2b6e5af9431d579ffb944517da25e8"}], "guid": "bfdfe7dc352907fc980b868725387e989d1d7458416ed56095a8ba9ad80f88c9", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e986c2aa6e287012b4ae1adb6afea457fd8", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ba0fbef6b2e2d81478a0376574814f13", "name": "FirebaseSharedSwift.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}