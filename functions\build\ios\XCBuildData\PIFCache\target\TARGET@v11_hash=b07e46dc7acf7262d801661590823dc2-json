{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9873e6611804ff5bbf004a19bcdae7ddd8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e9f6d4c27993251116b7b66523812878", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988fb785969940f8f2f9d535b04dc56765", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984b2c89bfa91392e942e91851d38a4118", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988fb785969940f8f2f9d535b04dc56765", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9824e35aa0cb5f1de1534cb4e953244a94", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9841c86644de07ab8199334ee53e7ea916", "guid": "bfdfe7dc352907fc980b868725387e98ef88f9da2c8ae989ad813a150a94392c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9804c820326203406562368ea4063a6af6", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f913f6d739507ba98c86e9b5d53ce161", "guid": "bfdfe7dc352907fc980b868725387e98c84bb34ad5ba64c81dcd43a7374edf39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98568a4c6d45df1f1e6c57a9732bfa7d04", "guid": "bfdfe7dc352907fc980b868725387e981d71eb8671284fd605f80dfa33e3729c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984539f6a49e25007299c580900166813d", "guid": "bfdfe7dc352907fc980b868725387e98fcde34f0c71aea6681a344e06646d0ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb5eb25e41a72b3ebd00330522c289d0", "guid": "bfdfe7dc352907fc980b868725387e982fe208f0ebe1251cbe4d36d0ea8481fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b846e8d34eab2a3064dbb92b82781c8", "guid": "bfdfe7dc352907fc980b868725387e98572198ff1b28a82666a80814ab06ca20"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981593d775d22f1c535ef376251356d832", "guid": "bfdfe7dc352907fc980b868725387e98d422562af953b776765c88ef8b4234a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b91b86cefe5a39a1e6780da2a6fa5b40", "guid": "bfdfe7dc352907fc980b868725387e98eb8a80e1b1bb3179870aae3276f42531"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98871fc37b033647093b04aec12d8e01c7", "guid": "bfdfe7dc352907fc980b868725387e987e2e2ae6bcc35c9c341227eca767675a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcdbcd8471e24e0c9114665d2c61df8b", "guid": "bfdfe7dc352907fc980b868725387e98289566deba40965ca80d83eb1c22eed8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864d5495eca0ab151d4ba6583cf9dffe6", "guid": "bfdfe7dc352907fc980b868725387e988dcf42985f2adb8d181908fffdc9c3c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcde704e005f0ef29ff9ebff01f61d68", "guid": "bfdfe7dc352907fc980b868725387e9866020bb5867edac92b91815d014d47e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841b03cc7aba7ba31c9a6388f021faa14", "guid": "bfdfe7dc352907fc980b868725387e987fc17762961812fc392098b828be1997"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855f4c0c53a783025163d56ebb79e47dd", "guid": "bfdfe7dc352907fc980b868725387e98932bf2e5d23bedf78ed2316ce81aaa5d"}], "guid": "bfdfe7dc352907fc980b868725387e9896f9de870adba0ab08d0ad2a51e0adcb", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e98737eba8e703402e7434c019fcea2fb2e"}], "guid": "bfdfe7dc352907fc980b868725387e988f5be42bc925af8b1ee85ef833f19aef", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98077be426f5004539f1b53198fc4d3f0a", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98799495a252f2286f7b65fbdcb452970d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}