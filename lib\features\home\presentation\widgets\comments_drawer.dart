import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:problem_perception_landing/design/app_colors.dart';
import 'package:problem_perception_landing/features/results/bloc/results_bloc.dart';
import 'package:problem_perception_landing/features/auth/bloc/auth_bloc.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:ui';

import '../../../../core/services/analytics_service.dart';
import '../../../../core/widgets/app_icon_button.dart';
import '../../../../service_locator.dart';
import '../../../payment/bloc/payment_bloc.dart';

class CommentsDrawer extends StatelessWidget {
  const CommentsDrawer({super.key});

  // Add this function to get the appropriate icon for each index
  Widget _getCommentEmoji(int index) {
    // Cycle through icons 0-5 for comments 0-15
    final iconIndex = index % 6;

    // Return the appropriate icon based on the index
    switch (iconIndex) {
      case 0:
        return Image.asset(
          'assets/images/animoji.png',
          height: 78,
          width: 78,
        );
      case 1:
        return Image.asset(
          'assets/images/animoji1.png',
          height: 78,
          width: 78,
        );
      case 2:
        return Image.asset(
          'assets/images/animoji2.png',
          height: 78,
          width: 78,
        );
      case 3:
        return Image.asset(
          'assets/images/animoji3.png',
          height: 78,
          width: 78,
        );
      case 4:
        return Image.asset(
          'assets/images/animoji4.png',
          height: 78,
          width: 78,
        );
      case 5:
        return Image.asset(
          'assets/images/animoji5.png',
          height: 78,
          width: 78,
        );
      default:
        return Image.asset(
          'assets/images/animoji5.png',
          height: 78,
          width: 78,
        );
    }
  }

  bool _hasSourceAccess(BuildContext context) {
    final authState = context.read<AuthBloc>().state;
    if (authState.user != null) {
      return authState.user!.plan == 'pro' || authState.user!.plan == 'enterprise';
    }
    return false;
  }

  Future<void> _launchURL(String urlString) async {
    final Uri? url = Uri.tryParse(urlString);
    if (url != null) {
      if (await canLaunchUrl(url)) {
        await launchUrl(url);
      }
    }
  }

  Widget _buildSourceWidget(BuildContext context, String url) {
    final hasAccess = _hasSourceAccess(context);

    return Stack(
      children: [
        // Wrap the logo with MouseRegion and GestureDetector if has access
        if (hasAccess)
          MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: () {
                _launchURL(url);
                sl<AnalyticsService>().logViewSourceClicked(url); // Optional: track clicks
              },
              child: _getLogoForDomain(url),
            ),
          )
        else
          _getLogoForDomain(url),
        if (!hasAccess)
          MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: () {
                showDialog(
                  barrierDismissible: false,
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('Upgrade Required'),
                    content: const Text('Upgrade to Pro to see source information'),
                    actions: [
                      TextButton(
                        onPressed: () {
                          sl<AnalyticsService>().logViewSourceUpgradeCancelled();
                          Navigator.pop(context);
                        },
                        child: const Text('Cancel'),
                      ),
                      TextButton(
                        onPressed: () {
                          sl<AnalyticsService>().logViewSourceUpgradeClicked();
                          context.read<PaymentBloc>().add(const CreateCheckoutSession('price_1PpVzXH6o3AOIeoGGwBGBK2z'));
                        },
                        child: const Text('Upgrade'),
                      ),
                    ],
                  ),
                );
              },
              child: ClipRect(
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.grey.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.lock, size: 16),
                        SizedBox(width: 4),
                        Text('Upgrade to Pro to see sources'),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }

  // Function to dynamically fetch the logo
  Widget _getLogoForDomain(String url) {
    final Uri? parsedUrl = Uri.tryParse(url);

    // If URL is invalid or domain is missing
    if (parsedUrl == null || parsedUrl.host.isEmpty) {
      return const Icon(Icons.link);
    }

    // Extract domain (e.g., "youtube.com")
    final domain = parsedUrl.host.replaceAll('www.', '');

    // Use Clearbit Logo API to fetch the logo dynamically
    final logoUrl = "https://logo.clearbit.com/$domain";

    return Row(
      children: [
        Image.network(
          logoUrl,
          height: 24,
          width: 24,
          errorBuilder: (context, error, stackTrace) {
            // Fallback to the Problem Perception logo if there's an error
            return const Icon(Icons.link, size: 24);
          },
        ),
        const SizedBox(width: 8),
        Text(
          domain.substring(0, domain.length - 4).replaceFirst(
                domain.substring(0, domain.length - 4)[0],
                domain.substring(0, domain.length - 4)[0].toUpperCase(),
              ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    var width = MediaQuery.of(context).size.width;
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, authState) {
        return Drawer(
          backgroundColor: AppColors.disabledColor,
          width: width > 1500 ? width * 0.65 : width * 0.85,
          child: Padding(
            padding: const EdgeInsets.fromLTRB(42, 42, 42, 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('References', style: Theme.of(context).textTheme.headlineMedium),
                const SizedBox(height: 24),
                Expanded(
                  child: BlocBuilder<ResultsBloc, ResultsState>(
                    builder: (context, state) {
                      if (state.selectedResult.comments == null || state.selectedResult.comments!.isEmpty) {
                        return const Center(
                          child: Text('No references available'),
                        );
                      } else {
                        return ListView.builder(
                          itemCount: state.selectedResult.comments!.length,
                          itemBuilder: (context, index) {
                            final comment = state.selectedResult.comments![index];
                            return Stack(
                              children: [
                                Container(
                                  padding: const EdgeInsets.fromLTRB(20, 20, 20, 0),
                                  margin: const EdgeInsets.only(bottom: 25),
                                  decoration: BoxDecoration(
                                    color: const Color.fromRGBO(255, 255, 255, 1),
                                    border: Border.all(color: const Color(0xFFE8E8E8)),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.grey.withOpacity(0.2),
                                        spreadRadius: 1,
                                        blurRadius: 10,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Column(
                                    children: [
                                      Row(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          GestureDetector(
                                              onTap: () => sl<AnalyticsService>().logCommentEmojiClicked(),
                                              child: _getCommentEmoji(index)),
                                          const SizedBox(width: 16),
                                          Flexible(
                                            child: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  'User ${index + 1}',
                                                  style: Theme.of(context).textTheme.bodyLarge,
                                                ),
                                                const SizedBox(height: 8),
                                                Text('"${comment.text}"', maxLines: 5),
                                                const SizedBox(height: 16),
                                                _buildSourceWidget(context, comment.url),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 24),
                                    ],
                                  ),
                                ),
                                Positioned(
                                  bottom: 12,
                                  right: 10,
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: const Color.fromRGBO(255, 255, 255, 1),
                                      border: Border.all(color: const Color(0xFFE8E8E8)),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.grey.withOpacity(0.2),
                                          spreadRadius: 1,
                                          blurRadius: 10,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Row(
                                        crossAxisAlignment: CrossAxisAlignment.end,
                                        mainAxisAlignment: MainAxisAlignment.end,
                                        children: [
                                          AppIconButton(
                                            tooltipMessage: 'Good reference',
                                            icon: const Icon(Icons.thumb_up, size: 12),
                                            onTap: () {
                                              sl<AnalyticsService>().logCommentLiked(comment.text);
                                              ScaffoldMessenger.of(context).showSnackBar(
                                                const SnackBar(
                                                  content: Text('Reference liked'),
                                                ),
                                              );
                                            },
                                          ),
                                          const SizedBox(width: 8),
                                          AppIconButton(
                                            tooltipMessage: 'Bad reference',
                                            icon: const Icon(Icons.thumb_down, size: 12),
                                            onTap: () {
                                              sl<AnalyticsService>().logCommentDisliked(comment.text);
                                              ScaffoldMessenger.of(context).showSnackBar(
                                                const SnackBar(
                                                  content: Text('Reference disliked'),
                                                ),
                                              );
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            );
                          },
                        );
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
