{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9811d9390ae986cf93e8abbd45a2dc663b", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c68261a36cc4ecb0c26500f1e3bbbc77", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9811cb58b502ae6b968424573d1462f4d0", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988151f4049c26ee1dcaf7a9a8930d1175", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9811cb58b502ae6b968424573d1462f4d0", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d3584ccb2b386ca5d565a4af0ebd718b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985f3f4cfe7d11375fdf348d69d68a0b3e", "guid": "bfdfe7dc352907fc980b868725387e9800cd9b89d3ae669c044a30ada6730e23", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac4bc5f38338eb2e75433a05d82db9db", "guid": "bfdfe7dc352907fc980b868725387e98470ef46316352f889af1ae083eeb175e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c3253e60c0f53764100bf6fbf458bef", "guid": "bfdfe7dc352907fc980b868725387e98f2dfb31a7ea8102820ebcee631bbbdba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2e116ed55713d35d7589de5def29e5f", "guid": "bfdfe7dc352907fc980b868725387e988197267c43a06a0a52c6417818c59714", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d657bf91345336b28c1780af5feea6a9", "guid": "bfdfe7dc352907fc980b868725387e98d5d3d5b6b81940fd716b608dc1c99e21", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce58a353828d602d5516698b27fa1514", "guid": "bfdfe7dc352907fc980b868725387e9811fe184f1249c76c99f679c4e503a560", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f019b6526c3c9c6de0d6b2129e280508", "guid": "bfdfe7dc352907fc980b868725387e98b27ca2461d9649b52f54db5ea38984ef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983843c5ad0c5c436dec1581da7743b3e7", "guid": "bfdfe7dc352907fc980b868725387e988b7ddc6b51e2602f5c13f2c7b30249f4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e2ae78bcc79564e05ee71500605b1c5", "guid": "bfdfe7dc352907fc980b868725387e98f5081e05660ffd49eba0f99237750120", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981988cc001b882926683b78b444c67fdf", "guid": "bfdfe7dc352907fc980b868725387e986d0c51a4f831e2425a1dc5d33011603c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a573453eda6f2be9dad84eb4430cf04", "guid": "bfdfe7dc352907fc980b868725387e987fbc5a3fa4c46c65e7076baae0d9f20c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a41ffea17e443c9c8ebbf85488a693aa", "guid": "bfdfe7dc352907fc980b868725387e983eaaf1ba5ddfa1e180ccc564807590e5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831cc6a26161c0c1da75515436f9d18ce", "guid": "bfdfe7dc352907fc980b868725387e98e450a26c7c3e19133f4a318fe484818b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a03d447db5d371a12e3fb1938578408", "guid": "bfdfe7dc352907fc980b868725387e9803d73068c385318bbaa8cec3050791cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c4af962d22e2505d11c919c83c74bf9", "guid": "bfdfe7dc352907fc980b868725387e98406f20e1e8d628f75aefdc71e7be7334", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb91af1ed214b13aaac7d8aaf43dd53b", "guid": "bfdfe7dc352907fc980b868725387e98e65af54810428dc6c66e4ebaa57a0e60", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5b4fee22b4853127d724c4616bebd9b", "guid": "bfdfe7dc352907fc980b868725387e983c036caed2cbc8db306ad66e11356a2b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812db3eab85a8a846317bc43c3b772ad8", "guid": "bfdfe7dc352907fc980b868725387e98cc9f51125c93d4e9361f343648b16826", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a150aa1f13de5bd9e0a5b792093c7043", "guid": "bfdfe7dc352907fc980b868725387e9820d7ec16e7999b5e6899451221c89313", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ddd8279772c4a84d0d7d89ba8f3415a", "guid": "bfdfe7dc352907fc980b868725387e98783899510984358b2e9adcf96a212434", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873806182fb647310bcfa9df2dafd8822", "guid": "bfdfe7dc352907fc980b868725387e988428946245fc650e8a796ebe67befbf8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0e9d155eb1b9e59f5b0c087cb1be070", "guid": "bfdfe7dc352907fc980b868725387e98ff682d6ddc77af6e091df7cf695e26a2", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98256f463f65b9015b5c96a235c5075ef1", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983597dc807261e4aec70fa61204752876", "guid": "bfdfe7dc352907fc980b868725387e98a47688f40c090e47fa165e403f906dd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985eb50141fe82dae7742de06a68526049", "guid": "bfdfe7dc352907fc980b868725387e981a774ce97f6fd8482ca44258162d1411"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1aa4e1826691e1824d265d83632fc26", "guid": "bfdfe7dc352907fc980b868725387e98396295dd972872b0994d2a1881c14b5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a2cad2987b0144415f13d8875d1764e", "guid": "bfdfe7dc352907fc980b868725387e98ce8ea72d8739ea409a9a86b5bbae99df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a19ff1f7b435f7f60086605d0e48d28f", "guid": "bfdfe7dc352907fc980b868725387e984aa3370c58946698fb52c192903e20e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849688f5fcf00569a5a8709d97b77af9e", "guid": "bfdfe7dc352907fc980b868725387e98a71b0f4afaff2ffa17e511b2434e2ef1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b7b42710ac62cffc992267570ddc621", "guid": "bfdfe7dc352907fc980b868725387e98b193b10e8a0ba1806388b5b16dd67124"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f817e8fd5d077712f9c89aaacbb37d46", "guid": "bfdfe7dc352907fc980b868725387e9850092fb2ce2d4c68b9b8aa9aba83630f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814df79dfba641f37f688ac0796819d0b", "guid": "bfdfe7dc352907fc980b868725387e98073d517429df75e266b907e40f9684c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879dcc5f41dbeaae878de7fd7e12f8163", "guid": "bfdfe7dc352907fc980b868725387e98ef20bc09966f7639c8936a018397921e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804af9ef953139690846d969596ce0413", "guid": "bfdfe7dc352907fc980b868725387e98de9846d6e49f8173a40de456ef7421a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988289bdf411a57f3f2368eb683e4cf6a0", "guid": "bfdfe7dc352907fc980b868725387e98d2b759fe3c646101120691d1bf160fe4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e746390560e9fdae82a5c460f42cd06", "guid": "bfdfe7dc352907fc980b868725387e98786d2957384d4369ab36b499ecd4a5dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98931b8cff6cb018dfe27a5cff6dc358f6", "guid": "bfdfe7dc352907fc980b868725387e987395ef30dfef69519c671280b2cbc6b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b9bdf78efa60b7973c0f45cf936c422", "guid": "bfdfe7dc352907fc980b868725387e98580c8ca1d32ebc8fad27b510008533d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985367df45e9ef4387b349606e650f6d2d", "guid": "bfdfe7dc352907fc980b868725387e989973d0f545831f40bf09afedd5a12e91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f179b63e07dbee339e45ed964dacd524", "guid": "bfdfe7dc352907fc980b868725387e9800acbe5f5cce0eb5960fcd16f994dc25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bd58fb4d8a670b8c09559742eb6f362", "guid": "bfdfe7dc352907fc980b868725387e985a2a75eb07b5d1f2b55cab0487c4b937"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e347532348a6f2261db05dad6d399b6", "guid": "bfdfe7dc352907fc980b868725387e984eeaf04c608f3c4f14e7213ed4ab67f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7bd88f471060f8371a628fc9ac89544", "guid": "bfdfe7dc352907fc980b868725387e98d963e648c1b59ad45b0fce1f2c94c2c9"}], "guid": "bfdfe7dc352907fc980b868725387e98754fd6c6ea25ef4873e092c26873c6c7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e9863c93d3f146f00dfc46241f3df567f72"}], "guid": "bfdfe7dc352907fc980b868725387e989482519707e852b9008a1e5ab25a6852", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98f730d59b6862b193d7e5406bc7f9186d", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98330ea623619ecba6c274a03d72608e61", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}